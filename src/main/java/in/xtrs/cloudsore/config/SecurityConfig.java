package in.xtrs.cloudsore.config;

import in.xtrs.cloudsore.security.JwtAuthenticationEntryPoint;
import in.xtrs.cloudsore.security.JwtAuthenticationFilter;
import in.xtrs.cloudsore.security.OAuth2SuccessHandler;
import lombok.RequiredArgsConstructor;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.security.config.annotation.method.configuration.EnableMethodSecurity;
import org.springframework.security.config.annotation.web.builders.HttpSecurity;
import org.springframework.security.config.annotation.web.configuration.EnableWebSecurity;
import org.springframework.security.config.annotation.web.configurers.AbstractHttpConfigurer;
import org.springframework.security.config.http.SessionCreationPolicy;
import org.springframework.security.web.SecurityFilterChain;
import org.springframework.security.web.authentication.UsernamePasswordAuthenticationFilter;
import org.springframework.web.cors.CorsConfiguration;
import org.springframework.web.cors.CorsConfigurationSource;
import org.springframework.web.cors.UrlBasedCorsConfigurationSource;

import java.util.Arrays;
import java.util.List;

@Configuration
@EnableWebSecurity
@EnableMethodSecurity(prePostEnabled = true)
@RequiredArgsConstructor
public class SecurityConfig {

    private final JwtAuthenticationEntryPoint jwtAuthenticationEntryPoint;
    private final JwtAuthenticationFilter jwtAuthenticationFilter;
    private final OAuth2SuccessHandler oAuth2SuccessHandler;

    @Bean
    public SecurityFilterChain filterChain(HttpSecurity http) throws Exception {
        http.csrf(AbstractHttpConfigurer::disable)
            .cors(cors -> cors.configurationSource(corsConfigurationSource()))
            .sessionManagement(session -> session.sessionCreationPolicy(SessionCreationPolicy.STATELESS))
            .exceptionHandling(exception -> exception.authenticationEntryPoint(jwtAuthenticationEntryPoint))
            .authorizeHttpRequests(auth -> auth
                // Public endpoints
                .requestMatchers("/api/auth/**").permitAll()
                .requestMatchers("/api/public/**").permitAll()
                .requestMatchers("/login/oauth2/**").permitAll()
                .requestMatchers("/oauth2/**").permitAll()
                
                // SUPERADMIN only endpoints
                .requestMatchers("/api/superadmin/**").hasRole("SUPERADMIN")
                .requestMatchers("/api/stores/create").hasRole("SUPERADMIN")
                
                // ADMIN endpoints
                .requestMatchers("/api/admin/**").hasAnyRole("SUPERADMIN", "ADMIN")
                .requestMatchers("/api/stores/*/categories/**").hasAnyRole("SUPERADMIN", "ADMIN")
                .requestMatchers("/api/stores/*/products/**").hasAnyRole("SUPERADMIN", "ADMIN")
                .requestMatchers("/api/stores/*/tax").hasAnyRole("SUPERADMIN", "ADMIN")
                
                // USER endpoints (excluding orders)
                .requestMatchers("/api/user/**").hasAnyRole("SUPERADMIN", "ADMIN", "USER")
                .requestMatchers("/api/stores").hasAnyRole("SUPERADMIN", "ADMIN", "USER")
                .requestMatchers("/api/stores/*/categories").hasAnyRole("SUPERADMIN", "ADMIN", "USER")
                .requestMatchers("/api/stores/*/products").hasAnyRole("SUPERADMIN", "ADMIN", "USER")
                
                // Order endpoints - restricted from USER role as per requirement
                .requestMatchers("/api/orders/**").hasAnyRole("SUPERADMIN", "ADMIN")
                
                // All other requests require authentication
                .anyRequest().authenticated()
            )
            .oauth2Login(oauth2 -> oauth2
                .successHandler(oAuth2SuccessHandler)
                .failureUrl("/login?error=true")
            );

        http.addFilterBefore(jwtAuthenticationFilter, UsernamePasswordAuthenticationFilter.class);

        return http.build();
    }

    @Bean
    public CorsConfigurationSource corsConfigurationSource() {
        CorsConfiguration configuration = new CorsConfiguration();
        configuration.setAllowedOriginPatterns(List.of("*"));
        configuration.setAllowedMethods(Arrays.asList("GET", "POST", "PUT", "DELETE", "OPTIONS"));
        configuration.setAllowedHeaders(Arrays.asList("*"));
        configuration.setAllowCredentials(true);
        
        UrlBasedCorsConfigurationSource source = new UrlBasedCorsConfigurationSource();
        source.registerCorsConfiguration("/**", configuration);
        return source;
    }
}

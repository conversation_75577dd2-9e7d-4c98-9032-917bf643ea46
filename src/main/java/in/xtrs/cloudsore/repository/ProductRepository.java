package in.xtrs.cloudsore.repository;

import in.xtrs.cloudsore.entity.Category;
import in.xtrs.cloudsore.entity.Product;
import in.xtrs.cloudsore.entity.Store;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.util.List;

@Repository
public interface ProductRepository extends JpaRepository<Product, Long> {
    
    List<Product> findByStore(Store store);
    
    List<Product> findByCategory(Category category);
    
    List<Product> findByStoreAndIsActive(Store store, Boolean isActive);
    
    List<Product> findByCategoryAndIsActive(Category category, Boolean isActive);
    
    @Query("SELECT p FROM Product p WHERE p.store.id = :storeId AND p.isActive = true")
    List<Product> findActiveByStoreId(@Param("storeId") Long storeId);
    
    @Query("SELECT p FROM Product p WHERE p.category.id = :categoryId AND p.isActive = true")
    List<Product> findActiveByCategoryId(@Param("categoryId") Long categoryId);
    
    @Query("SELECT p FROM Product p WHERE p.name LIKE %:name% AND p.store = :store AND p.isActive = true")
    List<Product> findByNameContainingAndStoreAndIsActive(@Param("name") String name, @Param("store") Store store);
    
    @Query("SELECT p FROM Product p WHERE p.isFeatured = true AND p.isActive = true")
    List<Product> findFeaturedProducts();
    
    @Query("SELECT p FROM Product p WHERE p.isFeatured = true AND p.store.id = :storeId AND p.isActive = true")
    List<Product> findFeaturedProductsByStoreId(@Param("storeId") Long storeId);
}

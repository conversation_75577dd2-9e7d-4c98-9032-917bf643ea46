package in.xtrs.cloudsore.service;

import in.xtrs.cloudsore.entity.Category;
import in.xtrs.cloudsore.entity.Product;
import in.xtrs.cloudsore.entity.Store;
import in.xtrs.cloudsore.repository.CategoryRepository;
import in.xtrs.cloudsore.repository.ProductRepository;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;
import java.util.List;

@Service
@RequiredArgsConstructor
@Slf4j
@Transactional
public class ProductService {

    private final ProductRepository productRepository;
    private final CategoryRepository categoryRepository;

    public Category createCategory(String name, String description, String imageUrl, Store store) {
        if (categoryRepository.existsByNameAndStore(name, store)) {
            throw new RuntimeException("Category with name '" + name + "' already exists in this store");
        }
        
        Category category = new Category(name, description, store);
        category.setImageUrl(imageUrl);
        return categoryRepository.save(category);
    }

    public Category findCategoryById(Long id) {
        return categoryRepository.findById(id)
                .orElseThrow(() -> new RuntimeException("Category not found with id: " + id));
    }

    public List<Category> getCategoriesByStore(Store store) {
        return categoryRepository.findByStoreAndIsActive(store, true);
    }

    public List<Category> getActiveCategories(Long storeId) {
        return categoryRepository.findActiveByStoreId(storeId);
    }

    public Category updateCategory(Long id, String name, String description, String imageUrl) {
        Category category = findCategoryById(id);
        
        if (name != null && !name.trim().isEmpty()) {
            // Check if name already exists in the same store
            if (!category.getName().equals(name) && 
                categoryRepository.existsByNameAndStore(name, category.getStore())) {
                throw new RuntimeException("Category with name '" + name + "' already exists in this store");
            }
            category.setName(name);
        }
        if (description != null) {
            category.setDescription(description);
        }
        if (imageUrl != null) {
            category.setImageUrl(imageUrl);
        }
        
        return categoryRepository.save(category);
    }

    public void deleteCategory(Long id) {
        Category category = findCategoryById(id);
        category.setIsActive(false);
        categoryRepository.save(category);
    }

    public Product createProduct(String name, String description, BigDecimal price, 
                               BigDecimal discountPrice, Integer stockQuantity, 
                               String imageUrl, Long categoryId, Store store) {
        Category category = findCategoryById(categoryId);
        
        // Verify category belongs to the same store
        if (!category.getStore().getId().equals(store.getId())) {
            throw new RuntimeException("Category does not belong to this store");
        }
        
        Product product = new Product(name, description, price, category, store);
        product.setDiscountPrice(discountPrice);
        product.setStockQuantity(stockQuantity != null ? stockQuantity : 0);
        product.setImageUrl(imageUrl);
        
        return productRepository.save(product);
    }

    public Product findProductById(Long id) {
        return productRepository.findById(id)
                .orElseThrow(() -> new RuntimeException("Product not found with id: " + id));
    }

    public List<Product> getProductsByStore(Store store) {
        return productRepository.findByStoreAndIsActive(store, true);
    }

    public List<Product> getActiveProducts(Long storeId) {
        return productRepository.findActiveByStoreId(storeId);
    }

    public List<Product> getProductsByCategory(Long categoryId) {
        return productRepository.findActiveByCategoryId(categoryId);
    }

    public List<Product> getFeaturedProducts() {
        return productRepository.findFeaturedProducts();
    }

    public List<Product> getFeaturedProductsByStore(Long storeId) {
        return productRepository.findFeaturedProductsByStoreId(storeId);
    }

    public Product updateProduct(Long id, String name, String description, BigDecimal price,
                               BigDecimal discountPrice, Integer stockQuantity, String imageUrl,
                               Long categoryId) {
        Product product = findProductById(id);
        
        if (name != null && !name.trim().isEmpty()) {
            product.setName(name);
        }
        if (description != null) {
            product.setDescription(description);
        }
        if (price != null) {
            product.setPrice(price);
        }
        if (discountPrice != null) {
            product.setDiscountPrice(discountPrice);
        }
        if (stockQuantity != null) {
            product.setStockQuantity(stockQuantity);
        }
        if (imageUrl != null) {
            product.setImageUrl(imageUrl);
        }
        if (categoryId != null) {
            Category category = findCategoryById(categoryId);
            // Verify category belongs to the same store
            if (!category.getStore().getId().equals(product.getStore().getId())) {
                throw new RuntimeException("Category does not belong to this store");
            }
            product.setCategory(category);
        }
        
        return productRepository.save(product);
    }

    public Product toggleFeatured(Long id) {
        Product product = findProductById(id);
        product.setIsFeatured(!product.getIsFeatured());
        return productRepository.save(product);
    }

    public void deleteProduct(Long id) {
        Product product = findProductById(id);
        product.setIsActive(false);
        productRepository.save(product);
    }

    public List<Product> searchProducts(String name, Store store) {
        return productRepository.findByNameContainingAndStoreAndIsActive(name, store);
    }

    public Product updateStock(Long productId, Integer quantity) {
        Product product = findProductById(productId);
        product.setStockQuantity(quantity);
        return productRepository.save(product);
    }

    public boolean isStockAvailable(Long productId, Integer requestedQuantity) {
        Product product = findProductById(productId);
        return product.getStockQuantity() >= requestedQuantity;
    }
}

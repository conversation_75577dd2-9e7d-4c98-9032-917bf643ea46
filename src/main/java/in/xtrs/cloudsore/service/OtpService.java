package in.xtrs.cloudsore.service;

import com.twilio.Twilio;
import com.twilio.rest.api.v2010.account.Message;
import com.twilio.type.PhoneNumber;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import java.security.SecureRandom;
import java.time.LocalDateTime;

@Service
@Slf4j
public class OtpService {

    @Value("${twilio.account.sid}")
    private String accountSid;

    @Value("${twilio.auth.token}")
    private String authToken;

    @Value("${twilio.phone.number}")
    private String fromPhoneNumber;

    private final SecureRandom random = new SecureRandom();

    public void initializeTwilio() {
        if (accountSid != null && authToken != null && 
            !accountSid.equals("your-twilio-account-sid") && 
            !authToken.equals("your-twilio-auth-token")) {
            Twilio.init(accountSid, authToken);
        }
    }

    public String generateOtp() {
        int otp = 100000 + random.nextInt(900000); // 6-digit OTP
        return String.valueOf(otp);
    }

    public LocalDateTime getOtpExpiry() {
        return LocalDateTime.now().plusMinutes(5); // OTP expires in 5 minutes
    }

    public boolean sendOtp(String phoneNumber, String otp) {
        try {
            initializeTwilio();
            
            // For development/testing, log the OTP instead of sending SMS
            if (accountSid.equals("your-twilio-account-sid")) {
                log.info("OTP for {}: {}", phoneNumber, otp);
                return true;
            }

            String messageBody = "Your CloudStore verification code is: " + otp + ". This code will expire in 5 minutes.";
            
            Message message = Message.creator(
                    new PhoneNumber(phoneNumber),
                    new PhoneNumber(fromPhoneNumber),
                    messageBody
            ).create();

            log.info("OTP sent successfully to {}. Message SID: {}", phoneNumber, message.getSid());
            return true;
        } catch (Exception e) {
            log.error("Failed to send OTP to {}: {}", phoneNumber, e.getMessage());
            return false;
        }
    }

    public boolean isOtpValid(String providedOtp, String storedOtp, LocalDateTime otpExpiry) {
        if (storedOtp == null || otpExpiry == null) {
            return false;
        }
        
        if (LocalDateTime.now().isAfter(otpExpiry)) {
            return false;
        }
        
        return storedOtp.equals(providedOtp);
    }

    public String formatPhoneNumber(String phoneNumber) {
        // Remove all non-digit characters
        String cleaned = phoneNumber.replaceAll("[^\\d]", "");
        
        // Add country code if not present (assuming India +91)
        if (!cleaned.startsWith("91") && cleaned.length() == 10) {
            cleaned = "91" + cleaned;
        }
        
        // Add + prefix
        if (!cleaned.startsWith("+")) {
            cleaned = "+" + cleaned;
        }
        
        return cleaned;
    }
}

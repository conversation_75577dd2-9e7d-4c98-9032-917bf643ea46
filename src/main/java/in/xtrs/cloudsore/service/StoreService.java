package in.xtrs.cloudsore.service;

import in.xtrs.cloudsore.entity.Store;
import in.xtrs.cloudsore.entity.User;
import in.xtrs.cloudsore.enums.Role;
import in.xtrs.cloudsore.repository.StoreRepository;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;
import java.util.List;
import java.util.Optional;

@Service
@RequiredArgsConstructor
@Slf4j
@Transactional
public class StoreService {

    private final StoreRepository storeRepository;
    private final UserService userService;

    public Store createStore(String name, String description, String address, String adminEmail) {
        // Find or create admin user
        Optional<User> adminOpt = userService.findByEmailOptional(adminEmail);
        User admin;
        
        if (adminOpt.isPresent()) {
            admin = adminOpt.get();
            // Update role to ADMIN if not already
            if (admin.getRole() != Role.ADMIN) {
                admin.setRole(Role.ADMIN);
                userService.updateUser(admin);
            }
        } else {
            // Create new admin user
            admin = userService.createUser("Store Admin", adminEmail, Role.ADMIN);
        }
        
        // Check if admin already has a store
        if (storeRepository.existsByAdmin(admin)) {
            throw new RuntimeException("Admin already has a store assigned");
        }
        
        Store store = new Store(name, description, address, admin);
        return storeRepository.save(store);
    }

    public Store findById(Long id) {
        return storeRepository.findById(id)
                .orElseThrow(() -> new RuntimeException("Store not found with id: " + id));
    }

    public Store findByAdmin(User admin) {
        return storeRepository.findByAdmin(admin)
                .orElseThrow(() -> new RuntimeException("Store not found for admin: " + admin.getEmail()));
    }

    public Optional<Store> findByAdminOptional(User admin) {
        return storeRepository.findByAdmin(admin);
    }

    public List<Store> getAllStores() {
        return storeRepository.findAll();
    }

    public List<Store> getActiveStores() {
        return storeRepository.findAllActiveStores();
    }

    public Store updateStore(Long id, String name, String description, String address, String phoneNumber) {
        Store store = findById(id);
        
        if (name != null && !name.trim().isEmpty()) {
            store.setName(name);
        }
        if (description != null) {
            store.setDescription(description);
        }
        if (address != null && !address.trim().isEmpty()) {
            store.setAddress(address);
        }
        if (phoneNumber != null) {
            store.setPhoneNumber(phoneNumber);
        }
        
        return storeRepository.save(store);
    }

    public Store updateTaxRate(Long storeId, BigDecimal taxRate) {
        Store store = findById(storeId);
        
        if (taxRate.compareTo(BigDecimal.ZERO) < 0 || taxRate.compareTo(new BigDecimal("100")) > 0) {
            throw new RuntimeException("Tax rate must be between 0 and 100");
        }
        
        store.setTaxRate(taxRate);
        return storeRepository.save(store);
    }

    public void activateStore(Long id) {
        Store store = findById(id);
        store.setIsActive(true);
        storeRepository.save(store);
    }

    public void deactivateStore(Long id) {
        Store store = findById(id);
        store.setIsActive(false);
        storeRepository.save(store);
    }

    public void deleteStore(Long id) {
        Store store = findById(id);
        store.setIsActive(false);
        storeRepository.save(store);
    }

    public List<Store> searchStores(String name) {
        return storeRepository.findByNameContainingAndIsActive(name);
    }

    public boolean isAdminOfStore(User user, Long storeId) {
        if (user.getRole() != Role.ADMIN) {
            return false;
        }
        
        Optional<Store> storeOpt = storeRepository.findById(storeId);
        if (storeOpt.isEmpty()) {
            return false;
        }
        
        Store store = storeOpt.get();
        return store.getAdmin().getId().equals(user.getId());
    }

    public Store getStoreByAdminId(Long adminId) {
        User admin = userService.findById(adminId);
        return findByAdmin(admin);
    }
}

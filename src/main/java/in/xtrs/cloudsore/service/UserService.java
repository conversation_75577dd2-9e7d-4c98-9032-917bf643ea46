package in.xtrs.cloudsore.service;

import in.xtrs.cloudsore.entity.User;
import in.xtrs.cloudsore.enums.Role;
import in.xtrs.cloudsore.repository.UserRepository;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Optional;

@Service
@RequiredArgsConstructor
@Slf4j
@Transactional
public class UserService {

    private final UserRepository userRepository;
    private final OtpService otpService;

    public User createUser(String name, String email, Role role) {
        if (userRepository.existsByEmail(email)) {
            throw new RuntimeException("User with email " + email + " already exists");
        }
        
        User user = new User(name, email, role);
        user.setIsVerified(true); // Email users are considered verified
        return userRepository.save(user);
    }

    public User createUserWithPhone(String name, String phoneNumber, Role role) {
        String formattedPhone = otpService.formatPhoneNumber(phoneNumber);
        
        if (userRepository.existsByPhoneNumber(formattedPhone)) {
            throw new RuntimeException("User with phone number " + formattedPhone + " already exists");
        }
        
        User user = new User(name, formattedPhone, role, true);
        return userRepository.save(user);
    }

    public User findByEmail(String email) {
        return userRepository.findByEmail(email)
                .orElseThrow(() -> new RuntimeException("User not found with email: " + email));
    }

    public User findByPhoneNumber(String phoneNumber) {
        String formattedPhone = otpService.formatPhoneNumber(phoneNumber);
        return userRepository.findByPhoneNumber(formattedPhone)
                .orElseThrow(() -> new RuntimeException("User not found with phone number: " + formattedPhone));
    }

    public Optional<User> findByEmailOptional(String email) {
        return userRepository.findByEmail(email);
    }

    public Optional<User> findByPhoneNumberOptional(String phoneNumber) {
        String formattedPhone = otpService.formatPhoneNumber(phoneNumber);
        return userRepository.findByPhoneNumber(formattedPhone);
    }

    public User findById(Long id) {
        return userRepository.findById(id)
                .orElseThrow(() -> new RuntimeException("User not found with id: " + id));
    }

    public User findByGoogleId(String googleId) {
        return userRepository.findByGoogleId(googleId)
                .orElseThrow(() -> new RuntimeException("User not found with Google ID: " + googleId));
    }

    public Optional<User> findByGoogleIdOptional(String googleId) {
        return userRepository.findByGoogleId(googleId);
    }

    public User updateUser(User user) {
        return userRepository.save(user);
    }

    public void deleteUser(Long id) {
        User user = findById(id);
        user.setIsActive(false);
        userRepository.save(user);
    }

    public List<User> getAllUsers() {
        return userRepository.findAll();
    }

    public List<User> getUsersByRole(Role role) {
        return userRepository.findActiveUsersByRole(role);
    }

    public boolean sendOtpToUser(String phoneNumber) {
        String formattedPhone = otpService.formatPhoneNumber(phoneNumber);
        Optional<User> userOpt = userRepository.findByPhoneNumber(formattedPhone);
        
        if (userOpt.isEmpty()) {
            throw new RuntimeException("User not found with phone number: " + formattedPhone);
        }
        
        User user = userOpt.get();
        String otp = otpService.generateOtp();
        LocalDateTime otpExpiry = otpService.getOtpExpiry();
        
        user.setOtpCode(otp);
        user.setOtpExpiry(otpExpiry);
        userRepository.save(user);
        
        return otpService.sendOtp(formattedPhone, otp);
    }

    public boolean verifyOtp(String phoneNumber, String otp) {
        String formattedPhone = otpService.formatPhoneNumber(phoneNumber);
        Optional<User> userOpt = userRepository.findByPhoneNumber(formattedPhone);
        
        if (userOpt.isEmpty()) {
            return false;
        }
        
        User user = userOpt.get();
        boolean isValid = otpService.isOtpValid(otp, user.getOtpCode(), user.getOtpExpiry());
        
        if (isValid) {
            user.setIsVerified(true);
            user.setOtpCode(null);
            user.setOtpExpiry(null);
            userRepository.save(user);
        }
        
        return isValid;
    }

    public User createOrUpdateGoogleUser(String googleId, String email, String name, String profilePicture) {
        Optional<User> existingUser = userRepository.findByGoogleId(googleId);
        
        if (existingUser.isPresent()) {
            User user = existingUser.get();
            user.setName(name);
            user.setProfilePicture(profilePicture);
            user.setIsVerified(true);
            return userRepository.save(user);
        }
        
        // Check if user exists with same email
        Optional<User> emailUser = userRepository.findByEmail(email);
        if (emailUser.isPresent()) {
            User user = emailUser.get();
            user.setGoogleId(googleId);
            user.setProfilePicture(profilePicture);
            user.setIsVerified(true);
            return userRepository.save(user);
        }
        
        // Create new user
        User newUser = new User();
        newUser.setGoogleId(googleId);
        newUser.setEmail(email);
        newUser.setName(name);
        newUser.setProfilePicture(profilePicture);
        newUser.setRole(Role.USER);
        newUser.setIsVerified(true);
        
        return userRepository.save(newUser);
    }
}

package in.xtrs.cloudsore.service;

import in.xtrs.cloudsore.entity.*;
import in.xtrs.cloudsore.enums.OrderStatus;
import in.xtrs.cloudsore.repository.OrderItemRepository;
import in.xtrs.cloudsore.repository.OrderRepository;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.List;

@Service
@RequiredArgsConstructor
@Slf4j
@Transactional
public class OrderService {

    private final OrderRepository orderRepository;
    private final OrderItemRepository orderItemRepository;
    private final ProductService productService;

    public Order createOrder(User user, Store store, String deliveryAddress, String deliveryPhone, String notes) {
        String orderNumber = generateOrderNumber();
        Order order = new Order(orderNumber, user, store);
        order.setDeliveryAddress(deliveryAddress);
        order.setDeliveryPhone(deliveryPhone);
        order.setNotes(notes);
        
        return orderRepository.save(order);
    }

    public OrderItem addOrderItem(Long orderId, Long productId, Integer quantity) {
        Order order = findById(orderId);
        Product product = productService.findProductById(productId);
        
        // Check stock availability
        if (!productService.isStockAvailable(productId, quantity)) {
            throw new RuntimeException("Insufficient stock for product: " + product.getName());
        }
        
        // Use discount price if available, otherwise regular price
        BigDecimal unitPrice = product.getDiscountPrice() != null ? 
                              product.getDiscountPrice() : product.getPrice();
        
        OrderItem orderItem = new OrderItem(order, product, quantity, unitPrice);
        orderItem = orderItemRepository.save(orderItem);
        
        // Update order totals
        updateOrderTotals(order);
        
        // Update product stock
        int newStock = product.getStockQuantity() - quantity;
        productService.updateStock(productId, newStock);
        
        return orderItem;
    }

    public Order findById(Long id) {
        return orderRepository.findById(id)
                .orElseThrow(() -> new RuntimeException("Order not found with id: " + id));
    }

    public Order findByOrderNumber(String orderNumber) {
        return orderRepository.findByOrderNumber(orderNumber)
                .orElseThrow(() -> new RuntimeException("Order not found with order number: " + orderNumber));
    }

    public List<Order> getOrdersByUser(User user) {
        return orderRepository.findByUserIdOrderByCreatedAtDesc(user.getId());
    }

    public List<Order> getOrdersByStore(Store store) {
        return orderRepository.findByStoreIdOrderByCreatedAtDesc(store.getId());
    }

    public List<Order> getOrdersByStatus(OrderStatus status) {
        return orderRepository.findByStatus(status);
    }

    public Order updateOrderStatus(Long orderId, OrderStatus status) {
        Order order = findById(orderId);
        order.setStatus(status);
        return orderRepository.save(order);
    }

    public void cancelOrder(Long orderId) {
        Order order = findById(orderId);
        
        if (order.getStatus() == OrderStatus.DELIVERED) {
            throw new RuntimeException("Cannot cancel delivered order");
        }
        
        // Restore product stock
        List<OrderItem> orderItems = orderItemRepository.findByOrder(order);
        for (OrderItem item : orderItems) {
            Product product = item.getProduct();
            int restoredStock = product.getStockQuantity() + item.getQuantity();
            productService.updateStock(product.getId(), restoredStock);
        }
        
        order.setStatus(OrderStatus.CANCELLED);
        orderRepository.save(order);
    }

    public List<OrderItem> getOrderItems(Long orderId) {
        return orderItemRepository.findByOrderId(orderId);
    }

    private void updateOrderTotals(Order order) {
        List<OrderItem> orderItems = orderItemRepository.findByOrder(order);
        
        BigDecimal subtotal = orderItems.stream()
                .map(OrderItem::getTotalPrice)
                .reduce(BigDecimal.ZERO, BigDecimal::add);
        
        BigDecimal taxAmount = subtotal.multiply(order.getStore().getTaxRate())
                .divide(BigDecimal.valueOf(100));
        
        BigDecimal totalAmount = subtotal.add(taxAmount);
        
        order.setSubtotal(subtotal);
        order.setTaxAmount(taxAmount);
        order.setTotalAmount(totalAmount);
        
        orderRepository.save(order);
    }

    private String generateOrderNumber() {
        String timestamp = LocalDateTime.now().format(DateTimeFormatter.ofPattern("yyyyMMddHHmmss"));
        return "ORD" + timestamp;
    }

    public Long getOrderCountByStoreAndStatus(Long storeId, OrderStatus status) {
        return orderRepository.countByStoreIdAndStatus(storeId, status);
    }

    public List<Order> getOrdersBetweenDates(LocalDateTime startDate, LocalDateTime endDate) {
        return orderRepository.findByCreatedAtBetween(startDate, endDate);
    }
}

package in.xtrs.cloudsore.controller;

import in.xtrs.cloudsore.entity.Product;
import in.xtrs.cloudsore.entity.Store;
import in.xtrs.cloudsore.service.ProductService;
import in.xtrs.cloudsore.service.StoreService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import java.util.List;

@RestController
@RequestMapping("/api/public")
@RequiredArgsConstructor
@Slf4j
@CrossOrigin(origins = "*")
public class PublicController {

    private final StoreService storeService;
    private final ProductService productService;

    @GetMapping("/stores")
    public ResponseEntity<List<Store>> getPublicStores() {
        try {
            List<Store> stores = storeService.getActiveStores();
            return ResponseEntity.ok(stores);
        } catch (Exception e) {
            log.error("Get public stores failed: {}", e.getMessage());
            return ResponseEntity.badRequest().build();
        }
    }

    @GetMapping("/stores/{storeId}")
    public ResponseEntity<Store> getPublicStore(@PathVariable Long storeId) {
        try {
            Store store = storeService.findById(storeId);
            if (!store.getIsActive()) {
                return ResponseEntity.notFound().build();
            }
            return ResponseEntity.ok(store);
        } catch (Exception e) {
            log.error("Get public store failed: {}", e.getMessage());
            return ResponseEntity.notFound().build();
        }
    }

    @GetMapping("/stores/{storeId}/products")
    public ResponseEntity<List<Product>> getPublicProducts(@PathVariable Long storeId,
                                                         @RequestParam(required = false) Long categoryId) {
        try {
            List<Product> products;
            if (categoryId != null) {
                products = productService.getProductsByCategory(categoryId);
            } else {
                products = productService.getActiveProducts(storeId);
            }
            return ResponseEntity.ok(products);
        } catch (Exception e) {
            log.error("Get public products failed: {}", e.getMessage());
            return ResponseEntity.badRequest().build();
        }
    }

    @GetMapping("/stores/{storeId}/products/featured")
    public ResponseEntity<List<Product>> getPublicFeaturedProducts(@PathVariable Long storeId) {
        try {
            List<Product> products = productService.getFeaturedProductsByStore(storeId);
            return ResponseEntity.ok(products);
        } catch (Exception e) {
            log.error("Get public featured products failed: {}", e.getMessage());
            return ResponseEntity.badRequest().build();
        }
    }

    @GetMapping("/products/featured")
    public ResponseEntity<List<Product>> getAllFeaturedProducts() {
        try {
            List<Product> products = productService.getFeaturedProducts();
            return ResponseEntity.ok(products);
        } catch (Exception e) {
            log.error("Get all featured products failed: {}", e.getMessage());
            return ResponseEntity.badRequest().build();
        }
    }

    @GetMapping("/health")
    public ResponseEntity<String> healthCheck() {
        return ResponseEntity.ok("CloudStore API is running!");
    }
}

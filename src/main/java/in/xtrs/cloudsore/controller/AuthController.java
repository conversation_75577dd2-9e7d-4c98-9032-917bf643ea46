package in.xtrs.cloudsore.controller;

import in.xtrs.cloudsore.dto.AuthRequest;
import in.xtrs.cloudsore.dto.AuthResponse;
import in.xtrs.cloudsore.dto.OtpRequest;
import in.xtrs.cloudsore.dto.PhoneLoginRequest;
import in.xtrs.cloudsore.entity.User;
import in.xtrs.cloudsore.service.AuthService;
import jakarta.validation.Valid;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

@RestController
@RequestMapping("/api/auth")
@RequiredArgsConstructor
@Slf4j
@CrossOrigin(origins = "*")
public class AuthController {

    private final AuthService authService;

    @PostMapping("/register/phone")
    public ResponseEntity<AuthResponse> registerWithPhone(@Valid @RequestBody AuthRequest request) {
        try {
            String message = authService.registerWithPhone(request.getName(), request.getPhoneNumber());
            return ResponseEntity.ok(new AuthResponse(null, message, true));
        } catch (Exception e) {
            log.error("Phone registration failed: {}", e.getMessage());
            return ResponseEntity.badRequest()
                    .body(new AuthResponse(null, e.getMessage(), false));
        }
    }

    @PostMapping("/login/phone")
    public ResponseEntity<AuthResponse> loginWithPhone(@Valid @RequestBody PhoneLoginRequest request) {
        try {
            String message = authService.loginWithPhone(request.getPhoneNumber());
            return ResponseEntity.ok(new AuthResponse(null, message, true));
        } catch (Exception e) {
            log.error("Phone login failed: {}", e.getMessage());
            return ResponseEntity.badRequest()
                    .body(new AuthResponse(null, e.getMessage(), false));
        }
    }

    @PostMapping("/verify/otp")
    public ResponseEntity<AuthResponse> verifyOtp(@Valid @RequestBody OtpRequest request) {
        try {
            String token = authService.authenticateWithPhone(request.getPhoneNumber(), request.getOtp());
            return ResponseEntity.ok(new AuthResponse(token, "OTP verified successfully", true));
        } catch (Exception e) {
            log.error("OTP verification failed: {}", e.getMessage());
            return ResponseEntity.badRequest()
                    .body(new AuthResponse(null, e.getMessage(), false));
        }
    }

    @GetMapping("/me")
    public ResponseEntity<User> getCurrentUser(@RequestHeader("Authorization") String token) {
        try {
            User user = authService.getCurrentUser(token);
            return ResponseEntity.ok(user);
        } catch (Exception e) {
            log.error("Get current user failed: {}", e.getMessage());
            return ResponseEntity.badRequest().build();
        }
    }

    @PostMapping("/validate")
    public ResponseEntity<AuthResponse> validateToken(@RequestHeader("Authorization") String token) {
        try {
            boolean isValid = authService.isTokenValid(token);
            if (isValid) {
                return ResponseEntity.ok(new AuthResponse(null, "Token is valid", true));
            } else {
                return ResponseEntity.badRequest()
                        .body(new AuthResponse(null, "Token is invalid", false));
            }
        } catch (Exception e) {
            log.error("Token validation failed: {}", e.getMessage());
            return ResponseEntity.badRequest()
                    .body(new AuthResponse(null, "Token validation failed", false));
        }
    }
}

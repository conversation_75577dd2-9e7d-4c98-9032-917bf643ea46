package in.xtrs.cloudsore.controller;

import in.xtrs.cloudsore.dto.CreateCategoryRequest;
import in.xtrs.cloudsore.dto.CreateProductRequest;
import in.xtrs.cloudsore.dto.UpdateCategoryRequest;
import in.xtrs.cloudsore.dto.UpdateProductRequest;
import in.xtrs.cloudsore.entity.Category;
import in.xtrs.cloudsore.entity.Product;
import in.xtrs.cloudsore.entity.Store;
import in.xtrs.cloudsore.entity.User;
import in.xtrs.cloudsore.service.AuthService;
import in.xtrs.cloudsore.service.ProductService;
import in.xtrs.cloudsore.service.StoreService;
import jakarta.validation.Valid;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.ResponseEntity;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;

import java.util.List;

@RestController
@RequestMapping("/api/stores/{storeId}")
@RequiredArgsConstructor
@Slf4j
@CrossOrigin(origins = "*")
public class ProductController {

    private final ProductService productService;
    private final StoreService storeService;
    private final AuthService authService;

    // Category endpoints
    @PostMapping("/categories")
    @PreAuthorize("hasRole('SUPERADMIN') or (hasRole('ADMIN') and @storeService.isAdminOfStore(authentication.principal, #storeId))")
    public ResponseEntity<Category> createCategory(@PathVariable Long storeId,
                                                 @Valid @RequestBody CreateCategoryRequest request) {
        try {
            Store store = storeService.findById(storeId);
            Category category = productService.createCategory(
                request.getName(),
                request.getDescription(),
                request.getImageUrl(),
                store
            );
            return ResponseEntity.ok(category);
        } catch (Exception e) {
            log.error("Category creation failed: {}", e.getMessage());
            return ResponseEntity.badRequest().build();
        }
    }

    @GetMapping("/categories")
    public ResponseEntity<List<Category>> getCategories(@PathVariable Long storeId) {
        try {
            List<Category> categories = productService.getActiveCategories(storeId);
            return ResponseEntity.ok(categories);
        } catch (Exception e) {
            log.error("Get categories failed: {}", e.getMessage());
            return ResponseEntity.badRequest().build();
        }
    }

    @PutMapping("/categories/{categoryId}")
    @PreAuthorize("hasRole('SUPERADMIN') or (hasRole('ADMIN') and @storeService.isAdminOfStore(authentication.principal, #storeId))")
    public ResponseEntity<Category> updateCategory(@PathVariable Long storeId,
                                                 @PathVariable Long categoryId,
                                                 @Valid @RequestBody UpdateCategoryRequest request) {
        try {
            Category category = productService.updateCategory(
                categoryId,
                request.getName(),
                request.getDescription(),
                request.getImageUrl()
            );
            return ResponseEntity.ok(category);
        } catch (Exception e) {
            log.error("Category update failed: {}", e.getMessage());
            return ResponseEntity.badRequest().build();
        }
    }

    @DeleteMapping("/categories/{categoryId}")
    @PreAuthorize("hasRole('SUPERADMIN') or (hasRole('ADMIN') and @storeService.isAdminOfStore(authentication.principal, #storeId))")
    public ResponseEntity<Void> deleteCategory(@PathVariable Long storeId,
                                             @PathVariable Long categoryId) {
        try {
            productService.deleteCategory(categoryId);
            return ResponseEntity.ok().build();
        } catch (Exception e) {
            log.error("Category deletion failed: {}", e.getMessage());
            return ResponseEntity.badRequest().build();
        }
    }

    // Product endpoints
    @PostMapping("/products")
    @PreAuthorize("hasRole('SUPERADMIN') or (hasRole('ADMIN') and @storeService.isAdminOfStore(authentication.principal, #storeId))")
    public ResponseEntity<Product> createProduct(@PathVariable Long storeId,
                                               @Valid @RequestBody CreateProductRequest request) {
        try {
            Store store = storeService.findById(storeId);
            Product product = productService.createProduct(
                request.getName(),
                request.getDescription(),
                request.getPrice(),
                request.getDiscountPrice(),
                request.getStockQuantity(),
                request.getImageUrl(),
                request.getCategoryId(),
                store
            );
            return ResponseEntity.ok(product);
        } catch (Exception e) {
            log.error("Product creation failed: {}", e.getMessage());
            return ResponseEntity.badRequest().build();
        }
    }

    @GetMapping("/products")
    public ResponseEntity<List<Product>> getProducts(@PathVariable Long storeId,
                                                   @RequestParam(required = false) Long categoryId) {
        try {
            List<Product> products;
            if (categoryId != null) {
                products = productService.getProductsByCategory(categoryId);
            } else {
                products = productService.getActiveProducts(storeId);
            }
            return ResponseEntity.ok(products);
        } catch (Exception e) {
            log.error("Get products failed: {}", e.getMessage());
            return ResponseEntity.badRequest().build();
        }
    }

    @GetMapping("/products/{productId}")
    public ResponseEntity<Product> getProduct(@PathVariable Long storeId,
                                            @PathVariable Long productId) {
        try {
            Product product = productService.findProductById(productId);
            return ResponseEntity.ok(product);
        } catch (Exception e) {
            log.error("Get product failed: {}", e.getMessage());
            return ResponseEntity.notFound().build();
        }
    }

    @PutMapping("/products/{productId}")
    @PreAuthorize("hasRole('SUPERADMIN') or (hasRole('ADMIN') and @storeService.isAdminOfStore(authentication.principal, #storeId))")
    public ResponseEntity<Product> updateProduct(@PathVariable Long storeId,
                                               @PathVariable Long productId,
                                               @Valid @RequestBody UpdateProductRequest request) {
        try {
            Product product = productService.updateProduct(
                productId,
                request.getName(),
                request.getDescription(),
                request.getPrice(),
                request.getDiscountPrice(),
                request.getStockQuantity(),
                request.getImageUrl(),
                request.getCategoryId()
            );
            return ResponseEntity.ok(product);
        } catch (Exception e) {
            log.error("Product update failed: {}", e.getMessage());
            return ResponseEntity.badRequest().build();
        }
    }

    @PutMapping("/products/{productId}/featured")
    @PreAuthorize("hasRole('SUPERADMIN') or (hasRole('ADMIN') and @storeService.isAdminOfStore(authentication.principal, #storeId))")
    public ResponseEntity<Product> toggleFeatured(@PathVariable Long storeId,
                                                @PathVariable Long productId) {
        try {
            Product product = productService.toggleFeatured(productId);
            return ResponseEntity.ok(product);
        } catch (Exception e) {
            log.error("Toggle featured failed: {}", e.getMessage());
            return ResponseEntity.badRequest().build();
        }
    }

    @DeleteMapping("/products/{productId}")
    @PreAuthorize("hasRole('SUPERADMIN') or (hasRole('ADMIN') and @storeService.isAdminOfStore(authentication.principal, #storeId))")
    public ResponseEntity<Void> deleteProduct(@PathVariable Long storeId,
                                            @PathVariable Long productId) {
        try {
            productService.deleteProduct(productId);
            return ResponseEntity.ok().build();
        } catch (Exception e) {
            log.error("Product deletion failed: {}", e.getMessage());
            return ResponseEntity.badRequest().build();
        }
    }

    @GetMapping("/products/featured")
    public ResponseEntity<List<Product>> getFeaturedProducts(@PathVariable Long storeId) {
        try {
            List<Product> products = productService.getFeaturedProductsByStore(storeId);
            return ResponseEntity.ok(products);
        } catch (Exception e) {
            log.error("Get featured products failed: {}", e.getMessage());
            return ResponseEntity.badRequest().build();
        }
    }
}

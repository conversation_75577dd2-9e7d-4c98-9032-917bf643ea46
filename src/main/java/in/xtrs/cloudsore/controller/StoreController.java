package in.xtrs.cloudsore.controller;

import in.xtrs.cloudsore.dto.CreateStoreRequest;
import in.xtrs.cloudsore.dto.UpdateStoreRequest;
import in.xtrs.cloudsore.dto.UpdateTaxRequest;
import in.xtrs.cloudsore.entity.Store;
import in.xtrs.cloudsore.entity.User;
import in.xtrs.cloudsore.service.AuthService;
import in.xtrs.cloudsore.service.StoreService;
import jakarta.validation.Valid;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.ResponseEntity;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;

import java.util.List;

@RestController
@RequestMapping("/api/stores")
@RequiredArgsConstructor
@Slf4j
@CrossOrigin(origins = "*")
public class StoreController {

    private final StoreService storeService;
    private final AuthService authService;

    @PostMapping("/create")
    @PreAuthorize("hasRole('SUPERADMIN')")
    public ResponseEntity<Store> createStore(@Valid @RequestBody CreateStoreRequest request) {
        try {
            Store store = storeService.createStore(
                request.getName(),
                request.getDescription(),
                request.getAddress(),
                request.getAdminEmail()
            );
            return ResponseEntity.ok(store);
        } catch (Exception e) {
            log.error("Store creation failed: {}", e.getMessage());
            return ResponseEntity.badRequest().build();
        }
    }

    @GetMapping
    public ResponseEntity<List<Store>> getAllStores() {
        try {
            List<Store> stores = storeService.getActiveStores();
            return ResponseEntity.ok(stores);
        } catch (Exception e) {
            log.error("Get all stores failed: {}", e.getMessage());
            return ResponseEntity.badRequest().build();
        }
    }

    @GetMapping("/{id}")
    public ResponseEntity<Store> getStore(@PathVariable Long id) {
        try {
            Store store = storeService.findById(id);
            return ResponseEntity.ok(store);
        } catch (Exception e) {
            log.error("Get store failed: {}", e.getMessage());
            return ResponseEntity.notFound().build();
        }
    }

    @PutMapping("/{id}")
    @PreAuthorize("hasRole('SUPERADMIN') or (hasRole('ADMIN') and @storeService.isAdminOfStore(authentication.principal, #id))")
    public ResponseEntity<Store> updateStore(@PathVariable Long id, 
                                           @Valid @RequestBody UpdateStoreRequest request) {
        try {
            Store store = storeService.updateStore(
                id,
                request.getName(),
                request.getDescription(),
                request.getAddress(),
                request.getPhoneNumber()
            );
            return ResponseEntity.ok(store);
        } catch (Exception e) {
            log.error("Store update failed: {}", e.getMessage());
            return ResponseEntity.badRequest().build();
        }
    }

    @PutMapping("/{id}/tax")
    @PreAuthorize("hasRole('SUPERADMIN') or (hasRole('ADMIN') and @storeService.isAdminOfStore(authentication.principal, #id))")
    public ResponseEntity<Store> updateTaxRate(@PathVariable Long id, 
                                             @Valid @RequestBody UpdateTaxRequest request) {
        try {
            Store store = storeService.updateTaxRate(id, request.getTaxRate());
            return ResponseEntity.ok(store);
        } catch (Exception e) {
            log.error("Tax rate update failed: {}", e.getMessage());
            return ResponseEntity.badRequest().build();
        }
    }

    @DeleteMapping("/{id}")
    @PreAuthorize("hasRole('SUPERADMIN')")
    public ResponseEntity<Void> deleteStore(@PathVariable Long id) {
        try {
            storeService.deleteStore(id);
            return ResponseEntity.ok().build();
        } catch (Exception e) {
            log.error("Store deletion failed: {}", e.getMessage());
            return ResponseEntity.badRequest().build();
        }
    }

    @GetMapping("/my-store")
    @PreAuthorize("hasRole('ADMIN')")
    public ResponseEntity<Store> getMyStore(@RequestHeader("Authorization") String token) {
        try {
            User currentUser = authService.getCurrentUser(token);
            Store store = storeService.findByAdmin(currentUser);
            return ResponseEntity.ok(store);
        } catch (Exception e) {
            log.error("Get my store failed: {}", e.getMessage());
            return ResponseEntity.notFound().build();
        }
    }

    @GetMapping("/search")
    public ResponseEntity<List<Store>> searchStores(@RequestParam String name) {
        try {
            List<Store> stores = storeService.searchStores(name);
            return ResponseEntity.ok(stores);
        } catch (Exception e) {
            log.error("Store search failed: {}", e.getMessage());
            return ResponseEntity.badRequest().build();
        }
    }
}

package in.xtrs.cloudsore.enums;

/**
 * User roles in the system
 */
public enum Role {
    SUPERADMIN("SUPERADMIN"),
    ADMIN("ADMIN"),
    USER("USER"),
    GUEST("GUEST");

    private final String value;

    Role(String value) {
        this.value = value;
    }

    public String getValue() {
        return value;
    }

    @Override
    public String toString() {
        return value;
    }
}

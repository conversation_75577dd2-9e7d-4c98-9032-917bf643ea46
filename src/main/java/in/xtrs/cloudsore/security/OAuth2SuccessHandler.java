package in.xtrs.cloudsore.security;

import in.xtrs.cloudsore.service.AuthService;
import jakarta.servlet.http.HttpServletRequest;
import jakarta.servlet.http.HttpServletResponse;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.security.core.Authentication;
import org.springframework.security.oauth2.core.user.OAuth2User;
import org.springframework.security.web.authentication.SimpleUrlAuthenticationSuccessHandler;
import org.springframework.stereotype.Component;

import java.io.IOException;
import java.net.URLEncoder;
import java.nio.charset.StandardCharsets;

@Component
@RequiredArgsConstructor
@Slf4j
public class OAuth2SuccessHandler extends SimpleUrlAuthenticationSuccessHandler {

    private final AuthService authService;

    @Override
    public void onAuthenticationSuccess(HttpServletRequest request, HttpServletResponse response,
                                      Authentication authentication) throws IOException {
        
        OAuth2User oAuth2User = (OAuth2User) authentication.getPrincipal();
        
        try {
            String googleId = oAuth2User.getAttribute("sub");
            String email = oAuth2User.getAttribute("email");
            String name = oAuth2User.getAttribute("name");
            String profilePicture = oAuth2User.getAttribute("picture");
            
            log.info("OAuth2 login successful for user: {}", email);
            
            String token = authService.authenticateWithGoogle(googleId, email, name, profilePicture);
            
            // Redirect to frontend with token
            String redirectUrl = "http://localhost:3000/auth/callback?token=" + 
                               URLEncoder.encode(token, StandardCharsets.UTF_8);
            
            response.sendRedirect(redirectUrl);
            
        } catch (Exception e) {
            log.error("OAuth2 authentication failed: {}", e.getMessage());
            response.sendRedirect("http://localhost:3000/login?error=oauth_failed");
        }
    }
}
